"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import React from "react";
import { useRetrievePurchase } from "@/features/purchases/api/use-retrieve-purchase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Calendar, DollarSign, Package, User } from "lucide-react";
import { Loader2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { formatCurrency } from "@/lib/utils";

export default function PurchaseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const purchaseId = params.id as string;

  const { data: response, isLoading, error } = useRetrievePurchase(purchaseId);
  const purchase: any = response?.data;

  const getStatusColor = (status?: string) => {
    switch ((status ?? '').toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "draft":
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <div className="xl:col-span-2 space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error || !purchase) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">Purchase not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back
          </Button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold">Purchase Details</h1>
            <p className="text-muted-foreground text-sm sm:text-base">{purchase.code}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="xl:col-span-2 space-y-6">
          {/* Purchase Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" /> Purchase Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <Badge className={getStatusColor(purchase.status)}>
                    {String(purchase.status).toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Date</p>
                  <p className="font-medium">{formatDate(purchase.date)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                  <p className="text-lg font-bold">{formatCurrency(Number(purchase.total_amount))}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Debit Amount</p>
                  <p className="text-lg font-bold text-destructive">{formatCurrency(Number(purchase.debit_amount ?? 0))}</p>
                </div>
              </div>
              {purchase.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Description</p>
                  <p className="text-sm">{purchase.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" /> Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              {purchase.items && purchase.items.length > 0 ? (
                <div className="space-y-4">
                  {purchase.items.map((item: any) => (
                    <div
                      key={item.id}
                      className="border rounded-lg p-3 sm:p-4 cursor-pointer hover:bg-gray-50 transition"
                      onClick={() => {
                        if (item.inventory_item?.id) {
                          router.push(`/inventories/${item.inventory_item.id}`);
                        }
                      }}
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div>
                          <div className="font-medium">{item.inventory_item?.name ?? 'Item'}</div>
                          <div className="text-xs text-muted-foreground">
                            Unit: {item.unit?.name ?? '-'} · SKU: {item.inventory_item?.inventory_item_sku ?? '-'}
                          </div>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="font-semibold">{formatCurrency(Number(item.unit_price))}</p>
                          <p className="text-xs text-muted-foreground">
                            Qty: {item.quantity} · Total: {formatCurrency(Number(item.quantity) * Number(item.unit_price))}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No items recorded</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6 xl:space-y-6">
          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" /> Created By
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Name</p>
                <p className="font-medium">{purchase.user?.name || "N/A"}</p>
              </div>
              {purchase.user?.phone && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone</p>
                  <p className="font-medium">{purchase.user.phone}</p>
                </div>
              )}
              {purchase.user?.email && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <p className="font-medium">{purchase.user.email}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" /> Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Items</p>
                <p className="font-medium">{purchase.items?.length || 0}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Quantity</p>
                <p className="font-medium text-blue-600">
                  {purchase.items?.reduce((sum: number, i: any) => sum + Number(i.quantity || 0), 0)}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                <p className="font-medium text-green-600">{formatCurrency(Number(purchase.total_amount || 0))}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                <p className="font-medium">
                  {purchase.updated_at ? formatDistanceToNow(new Date(purchase.updated_at), { addSuffix: true }) : '-'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}