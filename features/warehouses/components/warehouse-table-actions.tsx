import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Eye, Loader2, MoreHorizontal, Trash } from "lucide-react";
import React from "react";
import { useRouter } from "next/navigation";
import { useConfirm } from "@/hooks/use-confirm";
import { useWareHouseFormModal } from "../hooks/use-warehouse-form-modal";
import { useDeleteWarehouse } from "../api/use-delete-warehouse";
import { Warehouse } from "../types/warehouses";

type Props = {
  warehouse: Warehouse;
  organization_id: string;
};

export const WarehouseTableActions = ({ warehouse }: Props) => {
  const router = useRouter();

    const { edit } = useWareHouseFormModal();

    const { mutate, isPending } = useDeleteWarehouse();
      
        const [DeletingDialog, confirmDelete] = useConfirm(
          "Delete Warehouse",
          "This action cannot be undone",
          "ghost"
        );
      
        const handleDelete = async () => {
          const ok = await confirmDelete();
          if (!ok) return;
          mutate(warehouse.id.toString(), {
            onSuccess: () => {
              console.log("warehouse deleted successfully");
            },
            onError: () => {
              console.error("Error deleting warehouse:", warehouse.id);
            },
          });
        };
  
  return (
    <>
        
    <DeletingDialog />

    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => router.push(`/settings/users/${warehouse.id}`)}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => edit(warehouse.id.toString())}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            className="text-red-600 flex"
            disabled={isPending}
            onClick={handleDelete}
          ><Trash color="red" /> Delete 
          {isPending && <Loader2 className="spin" />}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>

    </>

  );
};