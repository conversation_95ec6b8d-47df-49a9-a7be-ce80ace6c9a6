import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Eye, Loader2, MoreHorizontal, Trash, Edit } from "lucide-react";
import { Purchases } from "../types/purchases";
import { usePurchaseFormModal } from "../hooks/use-purchase-form-modal";
import { useDeletePurchases } from "../api/use-delete-purchase";
import { useConfirm } from "@/hooks/use-confirm";

type Props = {
  purchase: Purchases;
  organization_id: string;
};

export const PurchaseTableActions = ({ purchase }: Props) => {

  const { edit } = usePurchaseFormModal();
  
  const handleView = (id: number) => {
    window.location.href=`/purchases/${id}`;
    console.log('single purchase')
  };

 const { mutate, isPending } = useDeletePurchases();
  
    const [DeletingDialog, confirmDelete] = useConfirm(
      "Delete Purchase",
      "This action cannot be undone",
      "ghost"
    );
  
    const handleDelete = async () => {
      const ok = await confirmDelete();
      if (!ok) return;
      mutate(purchase.id.toString(), {
        onSuccess: () => {
          console.log("Purchase deleted successfully");
        },
        onError: () => {
          console.error("Error deleting purchase:", purchase.id);
        },
      });
    };

  return (
    <div className="flex items-center space-x-2"> 
     <DeletingDialog />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => handleView(purchase.id)}>
              <Eye className="mr-2 h-4 w-4" />
              View
            </DropdownMenuItem>

          <DropdownMenuItem onClick={() => edit(purchase.id.toString())}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>

            <DropdownMenuItem
              className="text-red-600 flex"
              disabled={isPending}
              onClick={handleDelete}
            >
              <Trash color="red" /> Delete{" "}
              {isPending && <Loader2 className="spin" />}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};