import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod'; // Added resolver import
import { Button } from '@/components/ui/button';
import { DottedSeparator } from '@/components/dotted-separator';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ColumnDef } from '@tanstack/react-table';
import { Edit3Icon, Trash2Icon } from 'lucide-react';
import { toast } from 'sonner';
import { createPurchaseSchema } from '../schemas';
import { useStateManager } from '@/hooks/use-context';
import { useCreatePurchase } from '../api/use-create-purchase';
import { useEditPurchase } from '../api/use-edit-purchase';
import { useFetchInventories } from '@/features/inventories/api/use-fetch-inventories';
import { InventoryItem } from '@/features/inventories/types/inventories';
import { DataTableWithoutNavigation } from '@/components/data-table-without-navigations';
import { useFetchWarehouses } from '@/features/warehouses/api/use-fetch-warehouses';
import { Warehouse } from '@/features/warehouses/types/warehouses';
import { useRetrievePurchase } from '../api/use-retrieve-purchase';
import { usePurchaseFormModal } from '../hooks/use-purchase-form-modal';

// Define Item type alias
type Item = {
  inv_item_id: string;
  conf_unit_id: string;
  quantity: string;
  unit_price: string;

};

interface PurchaseFormProps {
  onCancel: () => void;
  defaultValues?: z.infer<typeof createPurchaseSchema >;
}

const PurchaseForm = ({ onCancel }: PurchaseFormProps) => {
  const { user } = useStateManager();
  const organization_id = localStorage.getItem('current_organization') ?? undefined;
  const { data: inventories } = useFetchInventories({ organization_id: organization_id, query: "" });
  const createPurchase = useCreatePurchase();
  const { id } = usePurchaseFormModal();
  const editPurchase = useEditPurchase(id);
  const { data: purchase } = useRetrievePurchase(id);
  const { data: warehouses} = useFetchWarehouses({ organization_id: organization_id })

  const isSubmitting = createPurchase.isPending || editPurchase.isPending;


  // Use zodResolver with useForm
  const form = useForm<z.infer<typeof createPurchaseSchema>>({
    resolver: zodResolver(createPurchaseSchema),
    defaultValues: {
      user_id: user?.id ? String(user.id) : undefined,
      organization_id: organization_id,
      date: new Date().toISOString().split('T')[0],
      description: "",
      total_amount: "",
      sale_type: "cash",
       items: [],
       warehouse_id:""
    },
  });

  // Set default warehouse when warehouses data is loaded
  useEffect(() => {
    if (warehouses?.data?.[0]?.id && !form.getValues("warehouse_id")) {
      form.setValue("warehouse_id", String(warehouses.data[0].id));
    }
  }, [warehouses, form]);

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: "items",
  });

  const [newItem, setNewItem] = useState<Item>({
    inv_item_id: "",
    conf_unit_id: "",
    quantity: "",
    unit_price: "",
  });

  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [editingItem, setEditingItem] = useState<Item | null>(null);

  useEffect(() => {
    if (newItem.inv_item_id) {
      const selectedInventory: InventoryItem | undefined = inventories?.data?.find((inv: InventoryItem) => inv.id === Number(newItem.inv_item_id));
      if (selectedInventory) {
        setNewItem(prev => ({ ...prev, unit_price: String(selectedInventory.sale_price || "") }));
      }
    }
  }, [newItem.inv_item_id, inventories]);

  useEffect(() => {
    if (isEditing && editingItem?.inv_item_id) {
      const selectedInventory: InventoryItem | undefined = inventories?.data?.find(
        (inv: InventoryItem) => inv.id === Number(editingItem.inv_item_id)
      );
      if (selectedInventory) {
        setEditingItem(prev => prev ? { ...prev, unit_price: String(selectedInventory.sale_price || "") } : null);
      }
    }
  }, [editingItem?.inv_item_id, inventories, isEditing]);

  useEffect(() => {
    const totalSum = fields.reduce((sum, field) => {
      const quantity = parseFloat(field.quantity || "0");
      const unitPrice = parseFloat(field.unit_price || "0");
      return sum + quantity * unitPrice;
    }, 0);

    const currentTotal = parseFloat(form.getValues("total_amount") || "0");
    if (parseFloat(totalSum.toFixed(2)) !== currentTotal) {
      form.setValue("total_amount", totalSum.toFixed(2), { shouldDirty: false });
    }
  }, [fields, form]);

  // Prefill form when editing (id is set) and purchase data is loaded
  useEffect(() => {
    if (!id || !purchase?.data) return;

    const p: any = purchase.data;
    const dateStr = typeof p.date === "string" ? p.date.slice(0, 10) : new Date().toISOString().split("T")[0];

    const mappedItems = Array.isArray(p.items)
      ? p.items.map((it: any) => ({
          inv_item_id: it?.inv_item_id != null ? String(it.inv_item_id) : "",
          conf_unit_id: it?.conf_unit_id != null ? String(it.conf_unit_id) : "",
          quantity: it?.quantity != null ? String(it.quantity) : "",
          unit_price: it?.unit_price != null ? String(it.unit_price) : "",
        }))
      : [];

    form.reset({
      organization_id: p?.organization_id != null ? String(p.organization_id) : (organization_id || ""),
      user_id: p?.user_id != null ? String(p.user_id) : (user?.id ? String(user.id) : ""),
      warehouse_id: p?.warehouse_id != null ? String(p.warehouse_id) : form.getValues("warehouse_id"),
      description: p?.description ?? "",
      date: dateStr,
      total_amount: p?.total_amount != null ? String(p.total_amount) : "",
      sale_type: p?.is_debit_purchase ? "credit" : "cash",
      items: mappedItems,
    });

    // Auto-open the first item editor when editing an existing purchase
    if (Array.isArray(p.items) && p.items.length > 0) {
      setEditIndex(0);
      setEditingItem({
        inv_item_id: p.items[0]?.inv_item_id != null ? String(p.items[0].inv_item_id) : "",
        conf_unit_id: p.items[0]?.conf_unit_id != null ? String(p.items[0].conf_unit_id) : "",
        quantity: p.items[0]?.quantity != null ? String(p.items[0].quantity) : "",
        unit_price: p.items[0]?.unit_price != null ? String(p.items[0].unit_price) : "",
      });
      setIsEditing(true);
    }
  }, [id, purchase, form, organization_id, user]);


  const handleAddItem = () => {
    if (!newItem.inv_item_id || !newItem.conf_unit_id || !newItem.quantity || !newItem.unit_price ) {
      console.error("Please fill all required fields");
      return;
    }
    append(newItem);
    setNewItem({
      inv_item_id: "",
      conf_unit_id: "",
      quantity: "",
      unit_price: "",

    });
  };

  const handleEdit = (index: number) => {
    const item = form.getValues().items[index];
    setEditingItem({ ...item });
    setEditIndex(index);
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (editIndex !== null && editingItem) {
      update(editIndex, editingItem);
      setIsEditing(false);
      setEditingItem(null);
      setEditIndex(null);
    }
  };

  const itemColumns: ColumnDef<{
    id: number;
    itemName: string;
    unitName: string;
    quantity: string;
    unitPrice: string;
    total: string;
  }>[] = [
    { header: "Item", accessorKey: "itemName" },
    { header: "Unit", accessorKey: "unitName" },
    { header: "Quantity", accessorKey: "quantity" },
    { header: "Unit Price", accessorKey: "unitPrice" },
    { header: "Total", accessorKey: "total" },
    {
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button onClick={() => handleEdit(row.original.id)}><Edit3Icon /></Button>
          <Button variant="destructive" onClick={() => remove(row.original.id)}><Trash2Icon /></Button>
        </div>
      ),
    },
  ];

  const itemData = fields.map((field, index) => {
    const selectedInventory = inventories?.data?.find((inv: InventoryItem) => inv.id === Number(field.inv_item_id));
    interface Unit {
      id: number;
      name: string;
    }

    const unit: Unit | undefined = Array.isArray(selectedInventory?.units)
      ? selectedInventory.units.find((u: Unit) => u.id === Number(field.conf_unit_id))
      : undefined;
    const quantity = parseFloat(field.quantity || "0");
    const unitPrice = parseFloat(field.unit_price || "0");
    const total = (quantity * unitPrice).toFixed(2);
    return {
      id: index,
      item_id: field.inv_item_id,
      itemName: selectedInventory?.name || "Unknown",
      unitName: unit?.name || "Unknown",
      quantity: field.quantity,
      unitPrice: field.unit_price,
      total,
    };
  });


  const onSubmit = (values: z.infer<typeof createPurchaseSchema>) => {
    const payload = {
      ...values,
      items: values.items.map(item => {
        const mappedItem: any = {
          id: item.inv_item_id.toString(),
          conf_unit_id: item.conf_unit_id.toString(),
          quantity: item.quantity.toString(),
          unit_price: item.unit_price.toString(),
        };
        return mappedItem;
      }),
    };

    
    if (id) {
      editPurchase.mutate(payload as any, {
        onSuccess: () => {
          toast.success("Purchase updated successfully");
          form.reset();
          onCancel();
        },
        onError: (error: unknown) => {
          const message =
            typeof error === "object" && error !== null && "message" in error
              ? String((error as { message: unknown }).message)
              : String(error);
          toast.error(message);
        },
      });
      return;
    }

    createPurchase.mutate(payload as any, {
      onSuccess: () => {
        toast.success("Purchase created successfully");
        form.reset();
        onCancel();
      },
      onError: (error: unknown) => {
        const message =
          typeof error === "object" && error !== null && "message" in error
            ? String((error as { message: unknown }).message)
            : String(error);
        toast.error(message);
      },
    });
  };

  const renderItemInputs = (
    item: Item,
    setItem: React.Dispatch<React.SetStateAction<Item>>
  ) => {
    const selectedInventory: InventoryItem | undefined = item.inv_item_id
      ? inventories?.data?.find((inv: InventoryItem) => inv.id === Number(item.inv_item_id))
      : undefined;

    // Units list from selected inventory (may be empty)
    const unitsList: any[] = Array.isArray((selectedInventory as any)?.units)
      ? ((selectedInventory as any).units as any[])
      : [];

    // Fallback labels from purchase payload when inventories/units are not available
    const fallbackPurchaseItem: any | undefined = purchase?.data?.items?.find(
      (pi: any) => String(pi.inv_item_id) === item.inv_item_id
    );
    const fallbackInventoryName = fallbackPurchaseItem?.inventory_item?.name;
    const fallbackUnitName = fallbackPurchaseItem?.unit?.name;
    const hasSelectedUnitInList = unitsList.some((u: any) => String(u.id) === item.conf_unit_id);

    const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const quantity = e.target.value;
      setItem(prev => ({ ...prev, quantity }));
    };

    const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const unit_price = e.target.value;
      setItem(prev => ({ ...prev, unit_price }));
    };

    const total = (parseFloat(item.quantity || "0") * parseFloat(item.unit_price || "0")).toFixed(2);

    return (
      <div className="flex flex-col gap-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <div className="md:col-span-2">
            <FormItem>
              <FormLabel>Choose Inventory</FormLabel>
              <Select
                value={item.inv_item_id}
                onValueChange={(value) => {
                  const nextInventory: InventoryItem | undefined = inventories?.data?.find((inv: InventoryItem) => inv.id === Number(value));
                  const units: any[] = Array.isArray((nextInventory as any)?.units) ? (nextInventory as any).units : [];
                  const defaultUnitId = units.length > 0 ? String(units[0].id) : "";
                  setItem(prev => ({
                    ...prev,
                    inv_item_id: value,
                    conf_unit_id: defaultUnitId,
                  }));
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Inventory" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Inventories</SelectLabel>
                    {/* Fallback option so the current value displays even if not in inventories list */}
                    {!selectedInventory && item.inv_item_id && fallbackInventoryName && (
                      <SelectItem key={`fallback-inv-${item.inv_item_id}`} value={item.inv_item_id}>
                        {fallbackInventoryName}
                      </SelectItem>
                    )}
                    {inventories?.data?.map((inventory : InventoryItem) => (
                      <SelectItem key={inventory.id} value={inventory.id.toString()}>
                        {inventory.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </FormItem>
          </div>
          <div>
            <FormItem>
              <FormLabel>Unit</FormLabel>
              <Select
                value={item.conf_unit_id}
                onValueChange={(value) => setItem(prev => ({ ...prev, conf_unit_id: value }))}
                disabled={!selectedInventory && !fallbackUnitName}
              >
                <SelectTrigger>
                  <SelectValue placeholder={selectedInventory || fallbackUnitName ? "Select Unit" : "Select Inventory First"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Units</SelectLabel>
                    {/* Fallback unit option if units list is unavailable or does not include selected */}
                    {((!Array.isArray(selectedInventory?.units) || selectedInventory?.units?.length === 0) ||
                      (Array.isArray((selectedInventory as any)?.units) && !(selectedInventory as any).units.some((u: any) => String(u.id) === item.conf_unit_id))) &&
                      item.conf_unit_id && fallbackUnitName && (
                        <SelectItem key={`fallback-unit-${item.conf_unit_id}`} value={item.conf_unit_id}>
                          {fallbackUnitName}
                        </SelectItem>
                      )}
                    {Array.isArray((selectedInventory as any)?.units) && (selectedInventory as any).units.map((unit: any) => (
                      <SelectItem key={unit.id} value={unit.id?.toString?.() ?? String(unit.id)}>
                        {unit.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </FormItem>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">

          <div>
            <FormItem>
              <FormLabel>Quantity</FormLabel>
              <Input
                value={item.quantity}
                onChange={handleQuantityChange}
                placeholder="Enter Quantity"
                type="number"
              />
            </FormItem>
          </div>
          <div>
            <FormItem>
              <FormLabel>Unit Price</FormLabel>
              <Input
                value={item.unit_price}
                onChange={handleUnitPriceChange}
                placeholder="Enter Unit Price"
                type="number"
              />
            </FormItem>
          </div>
        </div>
        <div className="flex justify-end">
          <p>Total: {total}</p>
        </div>
      </div>
    );
  };

  return (
 <Card className="w-full h-full shadow-md">
  <CardHeader className="flex p-7">
    <CardTitle className="text-2xl font-extrabold">Create Purchases</CardTitle>
  </CardHeader>
  <div className="px-7">
    <DottedSeparator />
  </div>
  <CardContent className="p-7">
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="flex flex-col gap-y-4">
              {isEditing ? (
                <>
                  <h2 className="text-lg font-semibold mb-2">Edit Item</h2>
                  <div className="bg-gray-50 p-4 rounded-md">
                    {renderItemInputs(editingItem!, setEditingItem as React.Dispatch<React.SetStateAction<Item>>)}
                  </div>
                </>
              ) : (
                <>
                  <h2 className="text-lg font-semibold mb-2">Add Item</h2>
                  <div className="bg-gray-50 p-4 rounded-md">
                    {renderItemInputs(newItem, setNewItem)}
                  </div>
                </>
              )}
              <div className="flex justify-end gap-2 mt-4">
                {isEditing ? (
                  <>
                    <Button type="button" onClick={handleSaveEdit} className="bg-blue-500 text-white">
                      Save
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        setIsEditing(false);
                        setEditingItem(null);
                        setEditIndex(null);
                      }}
                      className="bg-gray-200 text-gray-700"
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <Button type="button" onClick={handleAddItem} >
                    Add Item
                  </Button>
                )}
              </div>
              <div>
                <DataTableWithoutNavigation columns={itemColumns} data={itemData}  />
                <DottedSeparator className="my-2" />
                <div className="flex flex-col items-end mt-4 space-y-2">
                  <div className="flex justify-between w-full">
                    <span className="text-xl font-bold">Total:</span>
                    <span className="text-xl font-bold text-green-600">{form.watch("total_amount")}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="md:col-span-1">
            <div className="flex flex-col gap-y-4">
              <h2 className="text-lg font-semibold mb-2">Purchase Details</h2>
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date</FormLabel>
                    <FormControl>
                      <Input {...field} type="date" className="p-2 border border-gray-300 rounded-md" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="warehouse_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Warehouse</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger className="p-2 border border-gray-300 rounded-md w-full">
                          <SelectValue placeholder="Select Warehouse" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Warehouses</SelectLabel>
                            {warehouses?.data?.map((warehouse: Warehouse) => (
                              <SelectItem key={warehouse.id} value={String(warehouse.id)}>
                                {warehouse.name}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Enter Description" className="p-2 border border-gray-300 rounded-md" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="total_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Amount</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Total Amount" type="number" readOnly className="p-2 border border-gray-300 rounded-md bg-gray-100" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="sale_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Type</FormLabel>
                    <div className="flex gap-4">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          value="cash"
                          checked={field.value === "cash"}
                          onChange={() => field.onChange("cash")}
                          className="form-radio"
                        />
                        <span>Cash</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          value="credit"
                          checked={field.value === "credit"}
                          onChange={() => field.onChange("credit")}
                          className="form-radio"
                        />
                        <span>Credit</span>
                      </label>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
        <DottedSeparator className="my-2" />
        <div className="flex items-center justify-end gap-2 mt-4">
          <Button
            type="button"
            onClick={onCancel}
            variant="secondary"
            disabled={createPurchase.isPending || editPurchase.isPending}
          >
            Cancel
          </Button>
          <Button
              type="submit"
              disabled={createPurchase.isPending || editPurchase.isPending}
          >
            {id ? (editPurchase.isPending ? "Updating Purchase" : "Update Purchase") : (createPurchase.isPending ? "Creating Purchase" : "Create Purchase")}
          </Button>
        </div>
      </form>
    </Form>
  </CardContent>
</Card>
  );
};

export default PurchaseForm;